<template>
  <div>
    <slot name="before" />
    <v-dialog
      v-model="dialog"
      :max-width="maxWidth"
      :persistent="loader"
      :fullscreen="fullscreenInternal"
      :scrollable="scrollable"
    >
      <v-card :loading="loader">
        <v-card-title class="title">
          <slot name="title">
            <span class="headline">
              <h5 class="text-uppercase">{{ title }}</h5>
            </span>
            <v-spacer />
            <v-btn
              icon
              text
              tile
              small
              dark
              @click="closeDialog"
            >
              <v-icon>
                mdi-close
              </v-icon>
            </v-btn>
          </slot>
        </v-card-title>
        <v-card-text>
          <v-form
            v-if="hasForm"
            ref="form"
            @submit="submit"
          >
            <slot />
          </v-form>
          <template v-else>
            <slot />
          </template>
        </v-card-text>
        <v-card-actions v-if="showActions">
          <slot name="actions">
            <v-spacer />
            <v-btn
              color="gray"
              text
              :disabled="loader"
              @click="closeDialog"
            >
              {{ cancelButtonText || $t('actions.cancel') }}
            </v-btn>
            <v-btn
              v-if="showConfirmButton"
              color="primary"
              :loading="loading"
              :disabled="loader"
              @click="submit"
            >
              {{ confirmButtonText || $t('actions.save') }}
            </v-btn>
          </slot>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  name: 'GenericModal',
  props: {
    title: {
      type: String,
      required: true,
    },
    confirmButtonText: {
      type: String,
      default: null,
    },
    cancelButtonText: {
      type: String,
      default: null,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    maxWidth: {
      type: [String, Number],
      default: '700',
    },
    fullscreen: {
      type: Boolean,
      default: false,
    },
    scrollable: {
      type: Boolean,
      default: false,
    },
    hasForm: {
      type: Boolean,
      default: true,
    },
    showActions: {
      type: Boolean,
      default: true,
    },
    showConfirmButton: {
      type: Boolean,
      default: true,
    },
    autoClearForm: {
      type: Boolean,
      default: true,
    },
  },
  data: () => ({
    dialog: false,
    loadingInternal: false,
  }),
  computed: {
    fullscreenInternal() {
      return this.fullscreen || this.$vuetify.breakpoint.smAndDown;
    },
    loader() {
      return this.loading || this.loadingInternal;
    },
  },
  methods: {
    validate() {
      if (this.hasForm && this.$refs.form) {
        return this.$refs.form.validate();
      }
      return true;
    },
    clear() {
      if (this.hasForm && this.$refs.form) {
        this.$refs.form.reset();
        this.$refs.form.resetValidation();
      }
    },
    beforeSubmit() {
      this.$emit('beforeSubmit');
    },
    submit() {
      this.beforeSubmit();
      if (this.validate()) {
        this.$emit('submit');
      }
    },
    openDialog() {
      this.dialog = true;
      this.$emit('open');
    },
    closeDialog() {
      if (this.autoClearForm) {
        this.$nextTick(() => {
          this.clear();
        });
      }
      this.$emit('beforeClose');
      this.dialog = false;
      this.$emit('close');
    },
  },
};
</script>
