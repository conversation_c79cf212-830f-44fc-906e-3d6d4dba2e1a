<template>
  <div>
    <v-tooltip bottom>
      <template #activator="{ on, attrs }">
        <v-btn
          tile
          rounded
          class="mr-2 my-1"
          elevation="1"
          v-bind="attrs"
          x-small
          fab
          color="secondary"
          v-on="on"
          @click="openDialog"
        >
          <v-icon>
            mdi-account-plus
          </v-icon>
        </v-btn>
      </template>
      <span>{{ $t('actions.invite_user') }}</span>
    </v-tooltip>

    <GenericModal
      :ref="'inviteModal'"
      :title="$t('actions.invite_user')"
      confirm-button-text="Wyślij zaproszenie"
      cancel-button-text="Powrót do listy"
      max-width="600"
      :loading="loaders.submit"
      @close="clearFormData"
      @submit="submit"
    >
      <v-layout wrap>
        <v-col
          cols="12"
          sm="12"
          md="12"
        >
          <v-alert
            :value="true"
            border="left"
            type="info"
            color="blue lighten-4"
            class="blue--text text--darken-2"
          >
            {{ $t('loyalApp_invitationInfo') }}
          </v-alert>
        </v-col>
      </v-layout>
      <v-container grid-list-md>
        <v-layout wrap>
          <v-col
            cols="12"
            sm="12"
            md="12"
          >
            <v-text-field
              v-model="email"
              prepend-icon="mdi-account"
              :label="$t('common_email') + ' *'"
              :rules="form.validationRules.email"
              required
              :validate-on-blur="form.validateOnBlur"
            />
            <v-autocomplete
              v-model="selectedPackage"
              :items="packagesOptions"
              item-value="id"
              item-text="displayText"
              prepend-icon="mdi-cash"
              label="Pakiet promocyjny"
              :loading="packagesLoader"
              :disabled="packagesLoader"
              clearable
            />

            <v-expansion-panels
              v-model="invoiceDataExpanded"
              class="mb-3"
            >
              <v-expansion-panel>
                <v-expansion-panel-header>
                  <v-row>
                    <v-icon>mdi-file-document-edit-outline</v-icon>
                    <h6 class="text-h6 ml-2">
                      Dane do faktury
                    </h6>
                  </v-row>
                </v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-text-field
                    v-model="invoiceData.taxNumber"
                    prepend-icon="mdi-card-account-details"
                    label="Numer NIP"
                    :validate-on-blur="form.validateOnBlur"
                  />
                  <v-text-field
                    v-model="invoiceData.name"
                    prepend-icon="mdi-domain"
                    label="Nazwa firmy"
                    :validate-on-blur="form.validateOnBlur"
                  />
                  <v-text-field
                    v-model="invoiceData.address"
                    prepend-icon="mdi-map-marker"
                    label="Adres"
                    :validate-on-blur="form.validateOnBlur"
                  />
                  <v-text-field
                    v-model="invoiceData.postCode"
                    prepend-icon="mdi-mailbox"
                    label="Kod pocztowy"
                    :validate-on-blur="form.validateOnBlur"
                  />
                  <v-text-field
                    v-model="invoiceData.city"
                    prepend-icon="mdi-city"
                    label="Miasto"
                    :validate-on-blur="form.validateOnBlur"
                  />
                  <v-select
                    v-model="invoiceData.country"
                    :items="countries"
                    item-text="name"
                    item-value="shortName"
                    prepend-icon="mdi-earth"
                    label="Kraj"
                    :loading="countriesLoader"
                    :disabled="countriesLoader"
                    clearable
                    :validate-on-blur="form.validateOnBlur"
                  />
                </v-expansion-panel-content>
              </v-expansion-panel>
            </v-expansion-panels>

            <small>*{{ $t('common_fieldRequired') }}</small>
          </v-col>
        </v-layout>
      </v-container>
    </GenericModal>
  </div>
</template>

<script>
import SnackbarMixin from '@components/mixins/SnackbarMixin.vue';
import GenericModal from '@components/common/Modal.vue';

export default {
  name: 'LoyalAppUserInviteModal',
  components: {
    GenericModal,
  },
  mixins: [
    SnackbarMixin,
  ],
  props: {
    app: {
      type: String,
      default: null,
    },
  },
  data() {
    return {
      loaders: {
        submit: false,
      },
      email: '',
      selectedPackage: null,
      packages: [],
      packagesLoader: false,
      invoiceDataExpanded: null,
      countries: [],
      countriesLoader: false,
      invoiceData: {
        name: '',
        taxNumber: '',
        address: '',
        postCode: '',
        city: '',
        country: '',
      },
      form: {
        validateOnBlur: true,
        valid: true,
        validationRules: {
          email: [
            (v) => /^([a-zA-Z0-9_\-.+]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$/.test(
              v,
            )
              || this.$t('loyalApp_invalidValue'),
          ],
        },
      },
    };
  },
  computed: {
    packagesOptions() {
      return this.packages.map((pkg) => ({
        ...pkg,
        displayText: `${pkg.title} (wartość: ${pkg.package_value})`,
      }));
    },
    companyData() {
      return {
        name: this.invoiceData.name === '' ? null : this.invoiceData.name || null,
        taxNumber: this.invoiceData.taxNumber === '' ? null : this.invoiceData.taxNumber || null,
        address: this.invoiceData.address === '' ? null : this.invoiceData.address || null,
        postCode: this.invoiceData.postCode === '' ? null : this.invoiceData.postCode || null,
        city: this.invoiceData.city === '' ? null : this.invoiceData.city || null,
        country: this.invoiceData.country === '' ? null : this.invoiceData.country || null,
      };
    },
  },
  created() {
    this.getPackages();
    this.getCountries();
  },
  methods: {
    openDialog() {
      this.$refs.inviteModal.openDialog();
    },
    async submit() {
      this.loaders.submit = true;
      try {
        const response = await this.axios.post('/api/gateway/wla-admin/users', {
          email: this.email,
          app: this.app,
          packageId: this.selectedPackage,
          companyData: this.companyData,
        });

        if (response.status === 200) {
          this.showSnackbar('success', this.$t('common_actionSucced'));
          this.$refs.inviteModal.closeDialog();
        }
      } catch (error) {
        if (error.request && error.request.status === 409) {
          this.showSnackbar('warning', this.$t('loyalApp_userAlreadyExists'));
        } else if (error.request && error.request.status === 400) {
          this.showSnackbar('warning', this.$t('loyalApp_accessDenied'));
        } else {
          this.showSnackbar('error', 'Błąd podczas wysyłania zaproszenia');
        }
      } finally {
        this.loaders.submit = false;
      }
    },
    clearFormData() {
      this.email = '';
      this.selectedPackage = null;
      this.invoiceDataExpanded = null;
      this.invoiceData = {
        taxNumber: '',
        name: '',
        address: '',
        postCode: '',
        city: '',
        country: '',
      };
    },
    async getPackages() {
      this.packagesLoader = true;
      try {
        const response = await this.axios.get('/api/loyalapp/packages', {
          params: {
            app: this.app,
          },
        });
        if (response.status === 200 && response.data.items) {
          this.packages = response.data.items.filter((pkg) => pkg.active);
        }
      } catch (error) {
        this.showSnackbar('error', 'Błąd podczas pobierania pakietów');
      } finally {
        this.packagesLoader = false;
      }
    },
    async getCountries() {
      this.countriesLoader = true;
      try {
        const response = await this.axios.get('/api/lists/countries');
        if (response.status === 200 && response.data) {
          this.countries = response.data;
        }
      } catch (error) {
        this.showSnackbar('error', 'Błąd podczas pobierania krajów');
      } finally {
        this.countriesLoader = false;
      }
    },
  },
};
</script>
